# PowerShell deployment script for Fireworks Near Me
# This script helps deploy the application to Vercel with proper domain configuration

Write-Host "🎆 Deploying Fireworks Near Me to Vercel..." -ForegroundColor Yellow

# Check if Vercel CLI is installed
if (-not (Get-Command vercel -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Vercel CLI not found. Installing..." -ForegroundColor Red
    npm install -g vercel
}

# Check if we're logged in to Vercel
Write-Host "🔐 Checking Vercel authentication..." -ForegroundColor Blue
$vercelAuth = vercel whoami 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Not logged in to Vercel. Please run 'vercel login' first." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Logged in as: $vercelAuth" -ForegroundColor Green

# Build the project
Write-Host "🔨 Building the project..." -ForegroundColor Blue
bun run build

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed. Please fix the errors and try again." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Build successful!" -ForegroundColor Green

# Deploy to Vercel
Write-Host "🚀 Deploying to Vercel..." -ForegroundColor Blue
vercel --prod

if ($LASTEXITCODE -eq 0) {
    Write-Host "🎉 Deployment successful!" -ForegroundColor Green
    Write-Host "📝 Next steps:" -ForegroundColor Yellow
    Write-Host "1. Go to your Vercel dashboard" -ForegroundColor White
    Write-Host "2. Navigate to your project settings" -ForegroundColor White
    Write-Host "3. Add your custom domain: fireworksnearme.top" -ForegroundColor White
    Write-Host "4. Configure DNS records as shown in Vercel" -ForegroundColor White
} else {
    Write-Host "❌ Deployment failed. Please check the errors above." -ForegroundColor Red
    exit 1
}
