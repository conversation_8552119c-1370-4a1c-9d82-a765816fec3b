"use client";

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { locales, defaultLocale } from '@/i18n';

export function useLanguagePreference() {
  const router = useRouter();
  const pathname = usePathname();
  const currentLocale = useLocale();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Get stored language preference
    const storedLanguage = localStorage.getItem('preferred-language');
    
    // If no stored preference, store current locale
    if (!storedLanguage) {
      localStorage.setItem('preferred-language', currentLocale);
      document.cookie = `preferred-language=${currentLocale}; path=/; max-age=${60 * 60 * 24 * 365}`;
      return;
    }

    // If stored preference is different from current locale and is valid, redirect
    if (storedLanguage !== currentLocale && locales.includes(storedLanguage as (typeof locales)[number])) {
      const newPathname = pathname.replace(`/${currentLocale}`, `/${storedLanguage}`);
      router.push(newPathname);
    }
  }, [currentLocale, pathname, router]);

  return {
    currentLocale,
    setLanguagePreference: (locale: string) => {
      localStorage.setItem('preferred-language', locale);
      document.cookie = `preferred-language=${locale}; path=/; max-age=${60 * 60 * 24 * 365}`;
    }
  };
}
